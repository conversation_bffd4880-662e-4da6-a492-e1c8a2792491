{"LOG": true, "API_TIMEOUT_MS": 600000, "Providers": [{"name": "deepseek", "api_base_url": "https://api.deepseek.com/chat/completions", "api_key": "sk-your-deepseek-api-key-here", "models": ["deepseek-chat", "deepseek-reasoner"], "transformer": {"use": ["deepseek"], "deepseek-chat": {"use": ["tooluse"]}}}, {"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "sk-your-openrouter-api-key-here", "models": ["anthropic/claude-3.5-sonnet", "google/gemini-2.5-pro-preview", "anthropic/claude-3.7-sonnet:thinking"], "transformer": {"use": ["openrouter"]}}, {"name": "ollama", "api_base_url": "http://localhost:11434/v1/chat/completions", "api_key": "ollama", "models": ["qwen2.5-coder:latest"], "transformer": {"use": []}}], "Router": {"default": "deepseek,deepseek-chat", "background": "o<PERSON>ma,qwen2.5-coder:latest", "think": "deepseek,deepseek-reasoner", "longContext": "openrouter,google/gemini-2.5-pro-preview", "longContextThreshold": 60000, "webSearch": "openrouter,anthropic/claude-3.5-sonnet"}}