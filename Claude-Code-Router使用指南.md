# Claude Code Router 使用指南

## 📋 目录
- [快速开始](#快速开始)
- [基本运行](#基本运行)
- [模型切换](#模型切换)
- [路由模式](#路由模式)
- [常用命令](#常用命令)
- [使用技巧](#使用技巧)
- [故障排除](#故障排除)

## ⚡ 快速开始

```bash
# 1. 启动服务
ccr start

# 2. 开始使用（推荐方式）
ccr code "你好"
```

**或者使用原生 claude 命令：**

```powershell
# PowerShell 环境变量设置
$env:ANTHROPIC_API_BASE_URL = "http://127.0.0.1:3456"
$env:ANTHROPIC_API_KEY = "dummy-key"
claude
```

```cmd
# CMD 环境变量设置
set ANTHROPIC_API_BASE_URL=http://127.0.0.1:3456
set ANTHROPIC_API_KEY=dummy-key
claude
```


## 🚀 基本运行

### ⚠️ 重要：两种使用方式

Claude Code Router 有两种使用方式，请根据你的需求选择：

#### 方式1：使用 `ccr code` 命令（推荐）

```bash
# 第一步：启动 Router 服务
ccr start

# 第二步：使用 ccr code 命令
ccr code "你好"
ccr code "帮我写一个Python函数"
ccr code  # 进入交互模式
```

**优点**：
- ✅ 不需要设置环境变量
- ✅ 不会与其他 Claude 配置冲突
- ✅ 更清晰明确

#### 方式2：使用原生 `claude` 命令

```bash
# 第一步：启动 Router 服务
ccr start

# 第二步：设置环境变量（让原生 claude 使用 Router）

**PowerShell 方式（推荐）：**
```powershell
$env:ANTHROPIC_API_BASE_URL = "http://127.0.0.1:3456"
$env:ANTHROPIC_API_KEY = "dummy-key"  # 任意值，Router 会忽略
```

**CMD 方式：**
```cmd
set ANTHROPIC_API_BASE_URL=http://127.0.0.1:3456
set ANTHROPIC_API_KEY=dummy-key
```

**Windows 系统环境变量方式（永久生效）：**
1. 按 `Win + R`，输入 `sysdm.cpl`，回车
2. 点击"高级"选项卡 → "环境变量"
3. 在"用户变量"中点击"新建"，添加：
   - 变量名：`ANTHROPIC_API_BASE_URL`
   - 变量值：`http://127.0.0.1:3456`
4. 再次点击"新建"，添加：
   - 变量名：`ANTHROPIC_API_KEY`
   - 变量值：`dummy-key`
5. 点击"确定"保存，重新打开命令行窗口

# 第三步：使用原生 claude 命令
claude
```

**优点**：
- ✅ 使用熟悉的 `claude` 命令
- ✅ 与原有工作流兼容

**缺点**：
- ⚠️ 需要设置环境变量
- ⚠️ 可能与其他配置冲突

#### 永久配置方式2（可选）

如果你想永久使用原生 `claude` 命令，有以下几种永久配置方法：

**方法A：Windows 系统环境变量（推荐，全局生效）**
1. 按 `Win + R`，输入 `sysdm.cpl`，回车
2. 点击"高级"选项卡 → "环境变量"
3. 在"用户变量"中添加上述两个环境变量
4. 重新打开命令行窗口即可生效

**方法B：PowerShell 配置文件（仅 PowerShell 生效）**
```powershell
# 编辑 PowerShell 配置文件
notepad $PROFILE

# 添加以下内容到文件中：
$env:ANTHROPIC_API_BASE_URL = "http://127.0.0.1:3456"
$env:ANTHROPIC_API_KEY = "dummy-key"
```

**方法C：批处理文件（手动执行）**
创建一个 `setup-claude.bat` 文件：
```batch
@echo off
set ANTHROPIC_API_BASE_URL=http://127.0.0.1:3456
set ANTHROPIC_API_KEY=dummy-key
echo 环境变量已设置，可以使用 claude 命令了
cmd /k
```
每次需要使用时运行这个批处理文件。

### 📋 使用场景对比

| 方法 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| **ccr code** | 日常使用 | 简单直接，无需配置 | 命令稍长 |
| **PowerShell 临时** | 偶尔使用 | 快速设置 | 关闭窗口失效 |
| **CMD 临时** | CMD 用户 | 兼容性好 | 关闭窗口失效 |
| **系统环境变量** | 长期使用 | 全局永久生效 | 需要重启命令行 |
| **PowerShell 配置** | PowerShell 重度用户 | 自动加载 | 仅限 PowerShell |
| **批处理文件** | 团队共享 | 便于分发 | 需要手动执行 |

### 服务管理命令

```bash
# 启动 Claude Code Router 服务
ccr start

# 查看服务状态
ccr status

# 重启服务（推荐，会自动后台运行）
ccr restart

# 停止服务
ccr stop
```

## 🔄 模型切换

### 可用的提供商和模型

| 提供商名称 | 模型 | 特点 |
|------------|------|------|
| `tokhubai1-4` | `claude-sonnet-4-20250514` | Claude 最新版本 |
| `anyrouter1-5` | `claude-opus-4-20250514` | Claude Opus |
| `gemini-pro` | `gemini-2.5-pro` | Google Gemini |
| `augment-claude` | `claude-4.0-agent` | 增强版 Claude |
| `glm45` | `zai-org/GLM-4.5` | 智谱 GLM |
| `kimi-k2` | `kimi-k2-0711-preview` | 月之暗面 Kimi |

### 切换命令
```bash
# 在对话中切换模型
/model tokhubai1,claude-sonnet-4-20250514
/model gemini-pro,gemini-2.5-pro
/model augment-claude,claude-4.0-agent
/model glm45,zai-org/GLM-4.5
/model kimi-k2,kimi-k2-0711-preview

# 直接指定模型启动
ccr code "/model gemini-pro,gemini-2.5-pro" "你好"
```

## 🎯 路由模式

### 智能路由
Claude Code Router 会根据任务类型自动选择最适合的模型：

```bash
# 使用特定路由模式
/route default      # 默认模式 (tokhubai1 + claude-sonnet-4)
/route background   # 后台任务 (anyrouter1 + claude-haiku)
/route think        # 思考模式 (augment-claude + claude-4.0-agent)
/route longContext  # 长上下文 (kimi-k2 + kimi-k2-preview)
/route webSearch    # 网络搜索 (gemini-pro + gemini-2.5-pro)
```

**🔄 路由切换行为：**
- 路由切换后会**保持在当前会话中**，不会自动返回默认
- 要返回默认设置，需要手动执行 `/route default`
- 重新启动 `ccr code` 会恢复到默认路由

### 路由使用场景

| 路由模式 | 适用场景 | 示例 |
|----------|----------|------|
| `default` | 日常编程任务 | 代码编写、调试 |
| `background` | 简单快速任务 | 代码格式化、简单问答 |
| `think` | 复杂思考任务 | 架构设计、算法优化 |
| `longContext` | 长文本处理 | 大文件分析、文档总结 |
| `webSearch` | 需要搜索的任务 | 技术调研、最新信息 |

## 📝 常用命令

### 对话中的命令
```bash
/help           # 显示帮助信息
/status         # 查看当前设置（包括当前使用的模型/路由）
/model          # 查看/切换当前模型
/route          # 查看/切换路由模式
/route default  # 返回默认路由设置
/clear          # 清除对话历史
/exit           # 退出对话
```

**💡 提示：** 使用 `/status` 可以随时查看当前使用的是哪个模型和路由模式。

### 文件操作
```bash
# 编辑文件
edit filename.py "修改这个函数"

# 查看文件
view filename.py

# 搜索代码
search "function name"

# 创建文件
create filename.py "写一个新的Python脚本"
```

## 💡 使用技巧

### 1. 根据任务选择合适的模型

```bash
# 代码编写 - 使用 Claude Sonnet
/model tokhubai1,claude-sonnet-4-20250514

# 创意思考 - 使用 Claude Opus  
/model anyrouter1,claude-opus-4-20250514

# 长文本处理 - 使用 Kimi
/model kimi-k2,kimi-k2-0711-preview

# 搜索任务 - 使用 Gemini
/model gemini-pro,gemini-2.5-pro
```

### 2. 利用路由自动化

```bash
# 让路由自动选择最佳模型
/route think "设计一个微服务架构"
/route longContext "分析这个10000行的代码文件"
/route webSearch "最新的React 19特性"
```

**⚠️ 重要说明：路由模式行为**

- **临时切换**：使用 `/route` 命令只对**当前这一次对话**生效
- **会话保持**：切换后，当前会话会继续使用选择的模型，直到：
  - 手动切换到其他模型/路由
  - 退出并重新启动 Claude Code
  - 使用 `/route default` 返回默认设置

**示例：**
```bash
# 1. 默认使用 tokhubai1 + claude-sonnet-4
ccr code "你好"

# 2. 切换到思考模式
/route think
# 现在使用 augment-claude + claude-4.0-agent

# 3. 继续对话仍然使用思考模式
"继续分析这个问题"  # 仍然是 augment-claude

# 4. 手动返回默认
/route default
# 现在又回到 tokhubai1 + claude-sonnet-4
```

### 3. 组合使用

```bash
# 先用快速模型获取概览
/route background "这个项目的主要功能是什么？"

# 再用强大模型深入分析
/route think "如何优化这个项目的性能？"
```

### 4. 模型特点利用

- **Claude Sonnet**: 平衡性能，适合日常编程
- **Claude Opus**: 最强推理，适合复杂问题
- **Gemini Pro**: 搜索能力强，适合信息查询
- **GLM-4.5**: 中文理解好，适合中文任务
- **Kimi**: 长上下文，适合大文件处理

## 🔧 故障排除

### 常见问题

#### 1. "Missing API key" 错误
```
Missing API key · Run /login
```
**原因**: 你运行的是原生 `claude` 命令，但没有配置环境变量

**解决方案**:
- **方案A**: 使用 `ccr code` 命令代替 `claude`
- **方案B**: 设置环境变量后再使用 `claude`：
  ```bash
  $env:ANTHROPIC_API_BASE_URL = "http://127.0.0.1:3456"
  $env:ANTHROPIC_API_KEY = "dummy-key"
  claude
  ```

#### 2. 认证冲突警告
```
⚠ Auth conflict: Both a token and an API key are set
```
**解决方案**: 删除环境变量中的 `ANTHROPIC_API_KEY` 和 `ANTHROPIC_AUTH_TOKEN`

#### 3. 模型切换失败
```bash
# 检查可用模型
ccr code "/help"

# 确认提供商名称和模型名称正确
/model tokhubai1,claude-sonnet-4-20250514
```

#### 4. 服务无响应
```bash
# 重启服务
ccr restart

# 检查服务状态
ccr status

# 查看日志
ccr logs
```

#### 5. API 错误
- **403 Forbidden**: API 密钥无效或过期
- **500 Internal Error**: 服务器问题，尝试切换其他提供商
- **Timeout**: 网络问题，检查网络连接

### 调试技巧

```bash
# 启用详细日志
ccr start --verbose

# 测试特定提供商
ccr code "/model anyrouter1,claude-sonnet-4-20250514" "测试"

# 查看配置
ccr config
```

## 🎓 进阶使用

### 1. 批量任务处理
```bash
# 使用后台模式处理多个简单任务
/route background
"格式化这个文件"
"检查语法错误"
"添加注释"
```

### 2. 工作流优化
```bash
# 1. 快速概览
/route background "项目结构分析"

# 2. 深入思考
/route think "架构改进建议"

# 3. 实施方案
/route default "具体实现代码"
```

### 3. 多模型协作
```bash
# Gemini 搜索最新信息
/model gemini-pro,gemini-2.5-pro "React 19 新特性"

# Claude 分析和实现
/model tokhubai1,claude-sonnet-4-20250514 "基于刚才的信息，写个示例"
```

## 📞 获取帮助

```bash
# 查看完整帮助
ccr code "/help"

# 查看当前状态
ccr code "/status"

# 查看可用模型
ccr code "/model"

# 查看路由配置
ccr code "/route"
```

---

**提示**: 刚开始使用时，建议先用默认配置熟悉基本操作，然后逐步尝试不同的模型和路由模式。每个模型都有自己的特点，多尝试找到最适合你工作流的组合！
